[2025-07-15T11:48:11.881Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T11:48:11.900Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T11:48:11.900Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T11:48:11.901Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T12:02:15.744Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T12:02:15.765Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T12:02:15.765Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T12:02:15.766Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T12:04:07.612Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T12:04:07.628Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T12:04:07.629Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T12:04:07.629Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T20:56:27.483Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T20:56:27.500Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T20:56:27.500Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T20:56:27.501Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T20:57:24.122Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T20:57:24.139Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T20:57:24.140Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T20:57:24.140Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T21:49:00.208Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:49:00.287Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T21:49:00.288Z] [INFO] Queue service initialized successfully
[2025-07-15T21:49:00.289Z] [INFO] All services initialized successfully
[2025-07-15T21:49:00.292Z] [INFO] Assessment Service running on port 3003
[2025-07-15T21:49:00.293Z] [INFO] Environment: development
[2025-07-15T21:50:52.135Z] [INFO] Shutting down gracefully...
[2025-07-15T21:50:52.141Z] [WARN] RabbitMQ connection closed
[2025-07-15T21:50:52.142Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T21:50:52.144Z] [INFO] Queue service closed successfully
[2025-07-15T21:50:52.145Z] [INFO] All services closed successfully
[2025-07-15T21:50:53.912Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:50:53.987Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T21:50:53.989Z] [INFO] Queue service initialized successfully
[2025-07-15T21:50:53.990Z] [INFO] All services initialized successfully
[2025-07-15T21:50:53.994Z] [INFO] Assessment Service running on port 3003
[2025-07-15T21:50:53.995Z] [INFO] Environment: development
[2025-07-15T21:54:01.637Z] [ERROR] Auth service health check failed {"error":"getaddrinfo ENOTFOUND auth-service","url":"http://auth-service:3001"}
[2025-07-15T21:54:01.644Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:54:01 +0000] "GET /health HTTP/1.1" 200 603 "-" "axios/1.10.0"
[2025-07-15T21:54:01.669Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:54:01 +0000] "POST /assessment/submit HTTP/1.1" 404 98 "-" "axios/1.10.0"
[2025-07-15T21:55:31.885Z] [INFO] Shutting down gracefully...
[2025-07-15T21:55:31.890Z] [WARN] RabbitMQ connection closed
[2025-07-15T21:55:31.891Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T21:55:31.892Z] [INFO] Queue service closed successfully
[2025-07-15T21:55:31.892Z] [INFO] All services closed successfully
[2025-07-15T21:55:32.904Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:55:32.977Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T21:55:32.980Z] [INFO] Queue service initialized successfully
[2025-07-15T21:55:32.981Z] [INFO] All services initialized successfully
[2025-07-15T21:55:32.984Z] [INFO] Assessment Service running on port 3003
[2025-07-15T21:55:32.986Z] [INFO] Environment: development
[2025-07-15T21:55:55.864Z] [ERROR] Auth service health check failed {"error":"getaddrinfo ENOTFOUND auth-service","url":"http://auth-service:3001"}
[2025-07-15T21:55:55.870Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:55:55 +0000] "GET /health HTTP/1.1" 200 601 "-" "axios/1.10.0"
[2025-07-15T21:55:55.898Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:55:55 +0000] "POST /test/submit HTTP/1.1" 404 92 "-" "axios/1.10.0"
[2025-07-15T21:57:03.251Z] [INFO] Shutting down gracefully...
[2025-07-15T21:57:03.256Z] [WARN] RabbitMQ connection closed
[2025-07-15T21:57:03.257Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T21:57:03.258Z] [INFO] Queue service closed successfully
[2025-07-15T21:57:03.259Z] [INFO] All services closed successfully
[2025-07-15T21:57:06.009Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:57:06.080Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T21:57:06.082Z] [INFO] Queue service initialized successfully
[2025-07-15T21:57:06.083Z] [INFO] All services initialized successfully
[2025-07-15T21:57:06.087Z] [INFO] Assessment Service running on port 3003
[2025-07-15T21:57:06.088Z] [INFO] Environment: development
[2025-07-15T21:57:19.674Z] [ERROR] Auth service health check failed {"error":"getaddrinfo ENOTFOUND auth-service","url":"http://auth-service:3001"}
[2025-07-15T21:57:19.680Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:57:19 +0000] "GET /health HTTP/1.1" 200 602 "-" "axios/1.10.0"
[2025-07-15T21:57:19.711Z] [INFO] Test assessment submission received {"userId":"test-user-*************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T21:57:19.719Z] [INFO] Job created {"jobId":"9af9a39d-a428-48b7-a016-128cd29917e3","userId":"test-user-*************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-15T21:57:19.723Z] [INFO] Assessment job published to queue {"jobId":"6f7da46c-ebcd-43b7-9fce-1be758bfaa2f","userId":"test-user-*************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-15T21:57:19.775Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:57:19 +0000] "POST /test/submit HTTP/1.1" 202 341 "-" "axios/1.10.0"
[2025-07-15T21:59:18.464Z] [WARN] RabbitMQ connection closed
[2025-07-15T21:59:23.471Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T21:59:23.474Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:59:23.478Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T21:59:23.479Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T21:59:28.485Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T21:59:28.486Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:59:28.489Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T21:59:28.490Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T21:59:33.496Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T21:59:33.498Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:59:33.555Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T22:01:19.348Z] [INFO] Shutting down gracefully...
[2025-07-15T22:01:19.353Z] [WARN] RabbitMQ connection closed
[2025-07-15T22:01:19.354Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T22:01:19.355Z] [INFO] Queue service closed successfully
[2025-07-15T22:01:19.355Z] [INFO] All services closed successfully
[2025-07-15T22:01:20.602Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:01:20.679Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T22:01:20.681Z] [INFO] Queue service initialized successfully
[2025-07-15T22:01:20.682Z] [INFO] All services initialized successfully
[2025-07-15T22:01:20.686Z] [INFO] Assessment Service running on port 3003
[2025-07-15T22:01:20.687Z] [INFO] Environment: development
[2025-07-15T22:24:18.737Z] [WARN] RabbitMQ connection closed
[2025-07-15T22:24:23.744Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:23.746Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:23.750Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:24:23.751Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:24:28.758Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:28.760Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:32.709Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:32.711Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:37.715Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:37.716Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:41.672Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:41.673Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:46.680Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:46.681Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:50.649Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:50.650Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:55.656Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:55.659Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:59.617Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:59.618Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:04.625Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:04.627Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:08.568Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:08.569Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:13.576Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:13.577Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:17.529Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:17.531Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:22.534Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:22.535Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:26.503Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:26.504Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:31.512Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:31.514Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:35.461Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:35.462Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:40.467Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:40.469Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:44.439Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:44.440Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:49.447Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:49.448Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:53.399Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:53.401Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:58.407Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:58.409Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:02.355Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:02.357Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:07.362Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:07.365Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:11.313Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:11.315Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:16.322Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:16.323Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:20.294Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:20.295Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:25.302Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:25.303Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:29.251Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:29.252Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:34.256Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:34.257Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:38.208Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:38.210Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:43.217Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:43.218Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:45.724Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:26:45.726Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:26:50.727Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:50.729Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:50.731Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:26:50.733Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:26:54.844Z] [INFO] Shutting down gracefully...
[2025-07-15T22:26:54.846Z] [ERROR] Error closing RabbitMQ connection {"error":"Channel closed"}
[2025-07-15T22:26:54.848Z] [ERROR] Failed to close queue service {"error":"Channel closed"}
[2025-07-15T22:26:54.849Z] [ERROR] Error during shutdown {"error":"Channel closed"}
[2025-07-15T22:27:17.503Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:27:17.609Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T22:27:17.611Z] [INFO] Queue service initialized successfully
[2025-07-15T22:27:17.612Z] [INFO] All services initialized successfully
[2025-07-15T22:27:17.618Z] [INFO] Assessment Service running on port 3003
[2025-07-15T22:27:17.620Z] [INFO] Environment: development
[2025-07-15T22:31:59.926Z] [INFO] ::ffff:********** - - [15/Jul/2025:22:31:59 +0000] "GET /health HTTP/1.1" 200 601 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-15T22:32:33.618Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T22:32:33.625Z] [ERROR] Token deduction failed: Auth service error {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","tokenAmount":1,"status":404,"statusText":"Not Found","data":{"success":false,"error":{"code":"NOT_FOUND","message":"Route POST /auth/deduct-tokens not found"}}}
[2025-07-15T22:32:33.627Z] [ERROR] Unhandled error occurred {"error":"Failed to deduct tokens","stack":"AppError: Failed to deduct tokens\n    at Object.deductTokens (/app/src/services/authService.js:145:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/assessments.js:39:27","url":"/assessments/submit","method":"POST","ip":"::ffff:**********","userAgent":"axios/1.10.0","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
[2025-07-15T22:32:33.629Z] [INFO] ::ffff:********** - - [15/Jul/2025:22:32:33 +0000] "POST /assessments/submit HTTP/1.1" 500 108 "-" "axios/1.10.0"
[2025-07-15T23:08:09.404Z] [INFO] Shutting down gracefully...
[2025-07-15T23:08:09.414Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:08:09.416Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:08:09.420Z] [INFO] Queue service closed successfully
[2025-07-15T23:08:09.422Z] [INFO] All services closed successfully
[2025-07-15T23:08:10.689Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:08:10.763Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:08:10.766Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:08:10.768Z] [ERROR] Failed to initialize RabbitMQ {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:08:10.769Z] [ERROR] Failed to initialize queue service {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:08:10.771Z] [ERROR] Failed to initialize services {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:10:26.657Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:10:26.726Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:10:26.729Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:10:26.731Z] [ERROR] Failed to initialize RabbitMQ {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:10:26.733Z] [ERROR] Failed to initialize queue service {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:10:26.734Z] [ERROR] Failed to initialize services {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:11:10.199Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:11:10.273Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:11:10.275Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:11:10.277Z] [ERROR] Failed to initialize RabbitMQ {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:11:10.279Z] [ERROR] Failed to initialize queue service {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:11:10.280Z] [ERROR] Failed to initialize services {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:11.601Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:12:11.739Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:11.745Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:12:11.748Z] [ERROR] Failed to initialize RabbitMQ {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:11.750Z] [ERROR] Failed to initialize queue service {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:11.752Z] [ERROR] Failed to initialize services {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:44.765Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:12:44.849Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:12:44.852Z] [INFO] Queue service initialized successfully
[2025-07-15T23:12:44.853Z] [INFO] All services initialized successfully
[2025-07-15T23:12:44.858Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:12:44.859Z] [INFO] Environment: development
[2025-07-15T23:13:02.474Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:13:02.483Z] [ERROR] Token deduction failed: Auth service error {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","tokenAmount":1,"status":401,"statusText":"Unauthorized","data":{"success":false,"error":{"code":"UNAUTHORIZED","message":"Invalid service key"}}}
[2025-07-15T23:13:02.485Z] [ERROR] Unhandled error occurred {"error":"Invalid or expired token","stack":"AppError: Invalid or expired token\n    at Object.deductTokens (/app/src/services/authService.js:145:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/assessments.js:39:27","url":"/assessments/submit","method":"POST","ip":"::ffff:**********","userAgent":"axios/1.10.0","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
[2025-07-15T23:13:02.489Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:13:02 +0000] "POST /assessments/submit HTTP/1.1" 401 99 "-" "axios/1.10.0"
[2025-07-15T23:13:41.762Z] [INFO] Shutting down gracefully...
[2025-07-15T23:13:41.771Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:13:41.774Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:13:41.776Z] [INFO] Queue service closed successfully
[2025-07-15T23:13:41.777Z] [INFO] All services closed successfully
[2025-07-15T23:13:42.913Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:13:42.985Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:13:42.986Z] [INFO] Queue service initialized successfully
[2025-07-15T23:13:42.987Z] [INFO] All services initialized successfully
[2025-07-15T23:13:42.992Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:13:42.993Z] [INFO] Environment: development
[2025-07-15T23:13:51.346Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:13:51.356Z] [ERROR] Token deduction failed: Auth service error {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","tokenAmount":1,"status":401,"statusText":"Unauthorized","data":{"success":false,"error":{"code":"UNAUTHORIZED","message":"Invalid service key"}}}
[2025-07-15T23:13:51.361Z] [ERROR] Unhandled error occurred {"error":"Invalid or expired token","stack":"AppError: Invalid or expired token\n    at Object.deductTokens (/app/src/services/authService.js:145:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/assessments.js:39:27","url":"/assessments/submit","method":"POST","ip":"::ffff:**********","userAgent":"axios/1.10.0","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
[2025-07-15T23:13:51.365Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:13:51 +0000] "POST /assessments/submit HTTP/1.1" 401 99 "-" "axios/1.10.0"
[2025-07-15T23:14:36.397Z] [INFO] Shutting down gracefully...
[2025-07-15T23:14:36.428Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:14:36.433Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:14:36.437Z] [INFO] Queue service closed successfully
[2025-07-15T23:14:36.442Z] [INFO] All services closed successfully
[2025-07-15T23:14:38.211Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:14:38.282Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:14:38.284Z] [INFO] Queue service initialized successfully
[2025-07-15T23:14:38.286Z] [INFO] All services initialized successfully
[2025-07-15T23:14:38.291Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:14:38.292Z] [INFO] Environment: development
[2025-07-15T23:14:45.745Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:14:45.754Z] [ERROR] Token deduction failed: Auth service error {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","tokenAmount":1,"status":401,"statusText":"Unauthorized","data":{"success":false,"error":{"code":"UNAUTHORIZED","message":"Invalid service key"}}}
[2025-07-15T23:14:45.757Z] [ERROR] Unhandled error occurred {"error":"Invalid or expired token","stack":"AppError: Invalid or expired token\n    at Object.deductTokens (/app/src/services/authService.js:145:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/assessments.js:39:27","url":"/assessments/submit","method":"POST","ip":"::ffff:**********","userAgent":"axios/1.10.0","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
[2025-07-15T23:14:45.762Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:14:45 +0000] "POST /assessments/submit HTTP/1.1" 401 99 "-" "axios/1.10.0"
[2025-07-15T23:15:48.494Z] [INFO] Shutting down gracefully...
[2025-07-15T23:15:48.505Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:15:48.509Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:15:48.513Z] [INFO] Queue service closed successfully
[2025-07-15T23:15:48.518Z] [INFO] All services closed successfully
[2025-07-15T23:15:59.324Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:15:59.408Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:15:59.410Z] [INFO] Queue service initialized successfully
[2025-07-15T23:15:59.412Z] [INFO] All services initialized successfully
[2025-07-15T23:15:59.416Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:15:59.418Z] [INFO] Environment: development
[2025-07-15T23:16:17.835Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:16:17.869Z] [INFO] Token deduction successful {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","deductedAmount":1,"remainingBalance":4}
[2025-07-15T23:16:17.871Z] [INFO] Job created {"jobId":"d1359cf8-37bc-4d7e-9170-3965a1a39b39","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","status":"queued"}
[2025-07-15T23:16:17.874Z] [INFO] Assessment job published to queue {"jobId":"449ede9e-62cb-436a-97fa-84abb17a1f2b","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-15T23:16:17.925Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:16:17 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-15T23:20:59.074Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:20:59.099Z] [INFO] Token deduction successful {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","deductedAmount":1,"remainingBalance":3}
[2025-07-15T23:20:59.102Z] [INFO] Job created {"jobId":"77de69ee-f397-45b2-b5f0-e60bc3335216","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","status":"queued"}
[2025-07-15T23:20:59.104Z] [INFO] Assessment job published to queue {"jobId":"53f944c9-339d-4bd5-95d4-dc6230fc7add","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-15T23:20:59.108Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:20:59 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-15T23:27:53.079Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:27:53.098Z] [INFO] Token deduction successful {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","deductedAmount":1,"remainingBalance":2}
[2025-07-15T23:27:53.100Z] [INFO] Job created {"jobId":"66d8f0e1-cca7-44f4-9484-8fba933cd464","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","status":"queued"}
[2025-07-15T23:27:53.102Z] [INFO] Assessment job published to queue {"jobId":"abaee042-b2b2-4913-a783-898e116fcc87","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-15T23:27:53.105Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:27:53 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-15T23:31:42.802Z] [INFO] Shutting down gracefully...
[2025-07-15T23:31:42.820Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:31:42.827Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:31:42.840Z] [INFO] Queue service closed successfully
[2025-07-15T23:31:42.843Z] [INFO] All services closed successfully
[2025-07-15T23:32:39.265Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:32:39.367Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:32:39.369Z] [INFO] Queue service initialized successfully
[2025-07-15T23:32:39.370Z] [INFO] All services initialized successfully
[2025-07-15T23:32:39.375Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:32:39.376Z] [INFO] Environment: development
[2025-07-16T01:20:08.326Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T01:20:08.361Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T01:20:08.361Z] [INFO] Queue service initialized successfully
[2025-07-16T01:20:08.362Z] [INFO] All services initialized successfully
[2025-07-16T01:20:08.365Z] [INFO] Assessment Service running on port 3003
[2025-07-16T01:20:08.365Z] [INFO] Environment: development
[2025-07-16T01:21:04.785Z] [INFO] ::1 - - [16/Jul/2025:01:21:04 +0000] "GET / HTTP/1.1" 200 120 "-" "axios/1.10.0"
[2025-07-16T01:41:14.802Z] [INFO] ::1 - - [16/Jul/2025:01:41:14 +0000] "GET / HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-16T01:43:54.481Z] [INFO] ::1 - - [16/Jul/2025:01:43:54 +0000] "GET / HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-16T06:16:13.289Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T06:16:13.331Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T06:16:13.332Z] [INFO] Queue service initialized successfully
[2025-07-16T06:16:13.332Z] [INFO] All services initialized successfully
[2025-07-16T06:16:13.337Z] [INFO] Assessment Service running on port 3003
[2025-07-16T06:16:13.338Z] [INFO] Environment: development
[2025-07-16T06:18:03.556Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T06:18:03.601Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T06:18:03.601Z] [INFO] Queue service initialized successfully
[2025-07-16T06:18:03.602Z] [INFO] All services initialized successfully
[2025-07-16T06:18:03.606Z] [INFO] Assessment Service running on port 3003
[2025-07-16T06:18:03.607Z] [INFO] Environment: development
[2025-07-16T06:20:59.362Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T06:20:59.401Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T06:20:59.401Z] [INFO] Queue service initialized successfully
[2025-07-16T06:20:59.402Z] [INFO] All services initialized successfully
[2025-07-16T06:20:59.406Z] [INFO] Assessment Service running on port 3003
[2025-07-16T06:20:59.407Z] [INFO] Environment: development
[2025-07-16T06:26:05.256Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T06:26:05.300Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T06:26:05.300Z] [INFO] Queue service initialized successfully
[2025-07-16T06:26:05.301Z] [INFO] All services initialized successfully
[2025-07-16T06:26:05.305Z] [INFO] Assessment Service running on port 3003
[2025-07-16T06:26:05.305Z] [INFO] Environment: development
[2025-07-16T06:55:55.112Z] [WARN] Validation error {"source":"body","errors":{"riasec.realistic":"Realistic score is required","riasec.investigative":"Investigative score is required","riasec.artistic":"Artistic score is required","riasec.social":"Social score is required","riasec.enterprising":"Enterprising score is required","riasec.conventional":"Conventional score is required","ocean.openness":"Openness score is required","ocean.conscientiousness":"Conscientiousness score is required","ocean.extraversion":"Extraversion score is required","ocean.agreeableness":"Agreeableness score is required","ocean.neuroticism":"Neuroticism score is required","viaIs.creativity":"VIA-IS assessment data is required","viaIs.curiosity":"VIA-IS assessment data is required","viaIs.judgment":"VIA-IS assessment data is required","viaIs.loveOfLearning":"VIA-IS assessment data is required","viaIs.perspective":"VIA-IS assessment data is required","viaIs.bravery":"VIA-IS assessment data is required","viaIs.perseverance":"VIA-IS assessment data is required","viaIs.honesty":"VIA-IS assessment data is required","viaIs.zest":"VIA-IS assessment data is required","viaIs.love":"VIA-IS assessment data is required","viaIs.kindness":"VIA-IS assessment data is required","viaIs.socialIntelligence":"VIA-IS assessment data is required","viaIs.teamwork":"VIA-IS assessment data is required","viaIs.fairness":"VIA-IS assessment data is required","viaIs.leadership":"VIA-IS assessment data is required","viaIs.forgiveness":"VIA-IS assessment data is required","viaIs.humility":"VIA-IS assessment data is required","viaIs.prudence":"VIA-IS assessment data is required","viaIs.selfRegulation":"VIA-IS assessment data is required","viaIs.appreciationOfBeauty":"VIA-IS assessment data is required","viaIs.gratitude":"VIA-IS assessment data is required","viaIs.hope":"VIA-IS assessment data is required","viaIs.humor":"VIA-IS assessment data is required","viaIs.spirituality":"VIA-IS assessment data is required"},"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309","url":"/assessments/submit"}
[2025-07-16T06:55:55.116Z] [INFO] ::1 - - [16/Jul/2025:06:55:55 +0000] "POST /assessments/submit HTTP/1.1" 400 2006 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-16T07:41:37.856Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T07:41:37.898Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T07:41:37.898Z] [INFO] Queue service initialized successfully
[2025-07-16T07:41:37.899Z] [INFO] All services initialized successfully
[2025-07-16T07:41:37.903Z] [INFO] Assessment Service running on port 3003
[2025-07-16T07:41:37.904Z] [INFO] Environment: development
[2025-07-16T07:43:01.970Z] [INFO] ::1 - - [16/Jul/2025:07:43:01 +0000] "GET /health HTTP/1.1" 200 590 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T07:43:01.983Z] [INFO] ::1 - - [16/Jul/2025:07:43:01 +0000] "GET /health HTTP/1.1" 200 590 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T07:43:08.204Z] [INFO] ::1 - - [16/Jul/2025:07:43:08 +0000] "GET /health HTTP/1.1" 200 590 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:05:23.613Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T08:05:23.655Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T08:05:23.655Z] [INFO] Queue service initialized successfully
[2025-07-16T08:05:23.656Z] [INFO] All services initialized successfully
[2025-07-16T08:05:23.659Z] [INFO] Assessment Service running on port 3003
[2025-07-16T08:05:23.660Z] [INFO] Environment: development
[2025-07-16T08:20:30.814Z] [INFO] ::1 - - [16/Jul/2025:08:20:30 +0000] "GET /health HTTP/1.1" 200 590 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:30.829Z] [INFO] ::1 - - [16/Jul/2025:08:20:30 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:52.833Z] [INFO] ::1 - - [16/Jul/2025:08:20:52 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:55.745Z] [INFO] ::1 - - [16/Jul/2025:08:20:55 +0000] "GET /health HTTP/1.1" 200 589 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:56.396Z] [INFO] ::1 - - [16/Jul/2025:08:20:56 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:56.766Z] [INFO] ::1 - - [16/Jul/2025:08:20:56 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:57.002Z] [INFO] ::1 - - [16/Jul/2025:08:20:57 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:35:15.593Z] [WARN] Validation error {"source":"body","errors":{"riasec.realistic":"Realistic score is required","riasec.investigative":"Investigative score is required","riasec.artistic":"Artistic score is required","riasec.social":"Social score is required","riasec.enterprising":"Enterprising score is required","riasec.conventional":"Conventional score is required","ocean.openness":"Openness score is required","ocean.conscientiousness":"Conscientiousness score is required","ocean.extraversion":"Extraversion score is required","ocean.agreeableness":"Agreeableness score is required","ocean.neuroticism":"Neuroticism score is required","viaIs.creativity":"VIA-IS assessment data is required","viaIs.curiosity":"VIA-IS assessment data is required","viaIs.judgment":"VIA-IS assessment data is required","viaIs.loveOfLearning":"VIA-IS assessment data is required","viaIs.perspective":"VIA-IS assessment data is required","viaIs.bravery":"VIA-IS assessment data is required","viaIs.perseverance":"VIA-IS assessment data is required","viaIs.honesty":"VIA-IS assessment data is required","viaIs.zest":"VIA-IS assessment data is required","viaIs.love":"VIA-IS assessment data is required","viaIs.kindness":"VIA-IS assessment data is required","viaIs.socialIntelligence":"VIA-IS assessment data is required","viaIs.teamwork":"VIA-IS assessment data is required","viaIs.fairness":"VIA-IS assessment data is required","viaIs.leadership":"VIA-IS assessment data is required","viaIs.forgiveness":"VIA-IS assessment data is required","viaIs.humility":"VIA-IS assessment data is required","viaIs.prudence":"VIA-IS assessment data is required","viaIs.selfRegulation":"VIA-IS assessment data is required","viaIs.appreciationOfBeauty":"VIA-IS assessment data is required","viaIs.gratitude":"VIA-IS assessment data is required","viaIs.hope":"VIA-IS assessment data is required","viaIs.humor":"VIA-IS assessment data is required","viaIs.spirituality":"VIA-IS assessment data is required"},"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309","url":"/assessments/submit"}
[2025-07-16T08:35:15.595Z] [INFO] ::1 - - [16/Jul/2025:08:35:15 +0000] "POST /assessments/submit HTTP/1.1" 400 2006 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-16T09:15:14.713Z] [INFO] ::1 - - [16/Jul/2025:09:15:14 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:15:14.769Z] [INFO] ::1 - - [16/Jul/2025:09:15:14 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:24:42.849Z] [INFO] ::1 - - [16/Jul/2025:09:24:42 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:25:24.200Z] [INFO] ::1 - - [16/Jul/2025:09:25:24 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:25:26.023Z] [WARN] Validation error {"source":"body","errors":{"riasec.investigative":"Investigative score must be an integer","riasec.artistic":"Artistic score must be an integer","riasec.enterprising":"Enterprising score must be an integer","riasec.conventional":"Conventional score must be an integer","ocean.conscientiousness":"Conscientiousness score must be an integer","ocean.extraversion":"Extraversion score must be an integer","ocean.agreeableness":"Agreeableness score must be an integer","ocean.neuroticism":"Neuroticism score must be an integer","viaIs.curiosity":"VIA-IS scores must be integers","viaIs.judgment":"VIA-IS scores must be integers","viaIs.loveOfLearning":"VIA-IS scores must be integers","viaIs.perspective":"VIA-IS scores must be integers","viaIs.bravery":"VIA-IS scores must be integers","viaIs.perseverance":"VIA-IS scores must be integers","viaIs.honesty":"VIA-IS scores must be integers","viaIs.zest":"VIA-IS scores must be integers","viaIs.love":"VIA-IS scores must be integers","viaIs.socialIntelligence":"VIA-IS scores must be integers","viaIs.teamwork":"VIA-IS scores must be integers","viaIs.fairness":"VIA-IS scores must be integers","viaIs.leadership":"VIA-IS scores must be integers","viaIs.forgiveness":"VIA-IS scores must be integers","viaIs.humility":"VIA-IS scores must be integers","viaIs.prudence":"VIA-IS scores must be integers","viaIs.selfRegulation":"VIA-IS scores must be integers","viaIs.appreciationOfBeauty":"VIA-IS scores must be integers","viaIs.gratitude":"VIA-IS scores must be integers","viaIs.hope":"VIA-IS scores must be integers","viaIs.humor":"VIA-IS scores must be integers"},"userId":"********-710f-4360-8dfa-4ce497f7e98f","url":"/assessments/submit"}
[2025-07-16T09:25:26.025Z] [INFO] ::1 - - [16/Jul/2025:09:25:26 +0000] "POST /assessments/submit HTTP/1.1" 400 1672 "-" "axios/1.10.0"
[2025-07-16T09:28:11.694Z] [INFO] ::1 - - [16/Jul/2025:09:28:11 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:28:13.511Z] [WARN] Validation error {"source":"body","errors":{"riasec.investigative":"Investigative score must be an integer","riasec.artistic":"Artistic score must be an integer","riasec.enterprising":"Enterprising score must be an integer","riasec.conventional":"Conventional score must be an integer","ocean.conscientiousness":"Conscientiousness score must be an integer","ocean.extraversion":"Extraversion score must be an integer","ocean.agreeableness":"Agreeableness score must be an integer","ocean.neuroticism":"Neuroticism score must be an integer","viaIs.curiosity":"VIA-IS scores must be integers","viaIs.judgment":"VIA-IS scores must be integers","viaIs.loveOfLearning":"VIA-IS scores must be integers","viaIs.perspective":"VIA-IS scores must be integers","viaIs.bravery":"VIA-IS scores must be integers","viaIs.perseverance":"VIA-IS scores must be integers","viaIs.honesty":"VIA-IS scores must be integers","viaIs.zest":"VIA-IS scores must be integers","viaIs.love":"VIA-IS scores must be integers","viaIs.socialIntelligence":"VIA-IS scores must be integers","viaIs.teamwork":"VIA-IS scores must be integers","viaIs.fairness":"VIA-IS scores must be integers","viaIs.leadership":"VIA-IS scores must be integers","viaIs.forgiveness":"VIA-IS scores must be integers","viaIs.humility":"VIA-IS scores must be integers","viaIs.prudence":"VIA-IS scores must be integers","viaIs.selfRegulation":"VIA-IS scores must be integers","viaIs.appreciationOfBeauty":"VIA-IS scores must be integers","viaIs.gratitude":"VIA-IS scores must be integers","viaIs.hope":"VIA-IS scores must be integers","viaIs.humor":"VIA-IS scores must be integers"},"userId":"********-710f-4360-8dfa-4ce497f7e98f","url":"/assessments/submit"}
[2025-07-16T09:28:13.513Z] [INFO] ::1 - - [16/Jul/2025:09:28:13 +0000] "POST /assessments/submit HTTP/1.1" 400 1672 "-" "axios/1.10.0"
[2025-07-16T09:30:43.072Z] [INFO] ::1 - - [16/Jul/2025:09:30:43 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:30:44.898Z] [INFO] Assessment submission received {"userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-16T09:30:44.913Z] [INFO] Token deduction successful {"userId":"********-710f-4360-8dfa-4ce497f7e98f","deductedAmount":1,"remainingBalance":2}
[2025-07-16T09:30:44.914Z] [INFO] Job created {"jobId":"9000c35a-afd1-4640-b27b-c41984feda9c","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-16T09:30:44.916Z] [INFO] Assessment job published to queue {"jobId":"dbe17a47-1b38-4503-9f23-488238e3b413","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-16T09:30:44.924Z] [INFO] ::1 - - [16/Jul/2025:09:30:44 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-16T09:30:45.944Z] [INFO] ::1 - - [16/Jul/2025:09:30:45 +0000] "GET /assessments/status/9000c35a-afd1-4640-b27b-c41984feda9c HTTP/1.1" 200 266 "-" "axios/1.10.0"
[2025-07-16T09:33:55.912Z] [INFO] Assessment submission received {"userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-16T09:33:55.923Z] [INFO] Token deduction successful {"userId":"********-710f-4360-8dfa-4ce497f7e98f","deductedAmount":1,"remainingBalance":1}
[2025-07-16T09:33:55.924Z] [INFO] Job created {"jobId":"756403f3-001c-4cdc-a371-************","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-16T09:33:55.924Z] [INFO] Assessment job published to queue {"jobId":"de2861b0-907c-4337-8deb-95203f5ad28c","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-16T09:33:55.927Z] [INFO] ::1 - - [16/Jul/2025:09:33:55 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-16T09:33:57.954Z] [INFO] ::1 - - [16/Jul/2025:09:33:57 +0000] "GET /assessments/status/756403f3-001c-4cdc-a371-************ HTTP/1.1" 200 266 "-" "axios/1.10.0"
[2025-07-16T09:34:02.097Z] [INFO] ::1 - - [16/Jul/2025:09:34:02 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:34:02.123Z] [INFO] ::1 - - [16/Jul/2025:09:34:02 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T11:58:48.525Z] [INFO] Assessment submission received {"userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-16T11:58:48.544Z] [INFO] Token deduction successful {"userId":"********-710f-4360-8dfa-4ce497f7e98f","deductedAmount":1,"remainingBalance":0}
[2025-07-16T11:58:48.545Z] [INFO] Job created {"jobId":"0024d442-aaff-4f57-970c-2955013f2595","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-16T11:58:48.546Z] [INFO] Assessment job published to queue {"jobId":"********-149b-472b-9538-3383cda58b5f","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-16T11:58:48.548Z] [INFO] ::1 - - [16/Jul/2025:11:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-16T11:58:50.572Z] [INFO] ::1 - - [16/Jul/2025:11:58:50 +0000] "GET /assessments/status/0024d442-aaff-4f57-970c-2955013f2595 HTTP/1.1" 200 266 "-" "axios/1.10.0"
[2025-07-16T11:58:54.729Z] [INFO] ::1 - - [16/Jul/2025:11:58:54 +0000] "GET /health HTTP/1.1" 200 593 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T11:58:54.755Z] [INFO] ::1 - - [16/Jul/2025:11:58:54 +0000] "GET /health HTTP/1.1" 200 593 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T16:26:02.495Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T16:26:02.538Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T16:26:02.538Z] [INFO] Queue service initialized successfully
[2025-07-16T16:26:02.539Z] [INFO] All services initialized successfully
[2025-07-16T16:26:02.543Z] [INFO] Assessment Service running on port 3003
[2025-07-16T16:26:02.543Z] [INFO] Environment: development
[2025-07-16T16:26:45.729Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T16:26:45.774Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T16:26:45.775Z] [INFO] Queue service initialized successfully
[2025-07-16T16:26:45.776Z] [INFO] All services initialized successfully
[2025-07-16T16:26:45.780Z] [INFO] Assessment Service running on port 3003
[2025-07-16T16:26:45.781Z] [INFO] Environment: development
[2025-07-16T22:44:43.719Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T22:44:43.765Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T22:44:43.766Z] [INFO] Queue service initialized successfully
[2025-07-16T22:44:43.766Z] [INFO] All services initialized successfully
[2025-07-16T22:44:43.770Z] [INFO] Assessment Service running on port 3003
[2025-07-16T22:44:43.770Z] [INFO] Environment: development
[2025-07-16T23:00:13.030Z] [INFO] ::1 - - [16/Jul/2025:23:00:13 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T23:00:13.040Z] [INFO] ::1 - - [16/Jul/2025:23:00:13 +0000] "GET /health HTTP/1.1" 200 589 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T23:00:14.736Z] [INFO] ::1 - - [16/Jul/2025:23:00:14 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-17T01:01:55.959Z] [INFO] Assessment submission received {"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T01:01:55.983Z] [INFO] Token deduction successful {"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","deductedAmount":1,"remainingBalance":2}
[2025-07-17T01:01:55.984Z] [INFO] Job created {"jobId":"e3c009ea-37a5-4db3-ad16-cd3747fdbcad","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T01:01:55.986Z] [INFO] Assessment job published to queue {"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T01:01:55.996Z] [INFO] ::1 - - [17/Jul/2025:01:01:55 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:01:56.053Z] [INFO] ::1 - - [17/Jul/2025:01:01:56 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:01:56.068Z] [INFO] ::1 - - [17/Jul/2025:01:01:56 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:01.022Z] [INFO] ::1 - - [17/Jul/2025:01:02:01 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:06.038Z] [INFO] ::1 - - [17/Jul/2025:01:02:06 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:11.019Z] [INFO] ::1 - - [17/Jul/2025:01:02:11 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:16.027Z] [INFO] ::1 - - [17/Jul/2025:01:02:16 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:21.022Z] [INFO] ::1 - - [17/Jul/2025:01:02:21 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:25.184Z] [INFO] ::1 - - [17/Jul/2025:01:02:25 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:25.199Z] [INFO] ::1 - - [17/Jul/2025:01:02:25 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:31:29.095Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T01:31:29.135Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T01:31:29.135Z] [INFO] Queue service initialized successfully
[2025-07-17T01:31:29.136Z] [INFO] All services initialized successfully
[2025-07-17T01:31:29.140Z] [INFO] Assessment Service running on port 3003
[2025-07-17T01:31:29.141Z] [INFO] Environment: development
[2025-07-17T01:35:12.350Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T01:35:12.379Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T01:35:12.379Z] [INFO] Queue service initialized successfully
[2025-07-17T01:35:12.380Z] [INFO] All services initialized successfully
[2025-07-17T01:35:12.382Z] [INFO] Assessment Service running on port 3003
[2025-07-17T01:35:12.382Z] [INFO] Environment: development
[2025-07-17T01:36:20.082Z] [INFO] Test assessment submission received {"userId":"test-user-*************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::1"}
[2025-07-17T01:36:20.083Z] [INFO] Job created {"jobId":"3708908e-587b-40a1-b1c8-98f790e0173a","userId":"test-user-*************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T01:36:20.086Z] [INFO] Assessment job published to queue {"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","userId":"test-user-*************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T01:36:20.093Z] [INFO] ::1 - - [17/Jul/2025:01:36:20 +0000] "POST /test/submit HTTP/1.1" 202 341 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-17T01:48:11.859Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T01:48:11.888Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T01:48:11.888Z] [INFO] Queue service initialized successfully
[2025-07-17T01:48:11.889Z] [INFO] All services initialized successfully
[2025-07-17T01:48:11.891Z] [INFO] Assessment Service running on port 3003
[2025-07-17T01:48:11.891Z] [INFO] Environment: development
[2025-07-17T01:48:18.685Z] [WARN] Validation error {"source":"body","errors":{"riasec":"RIASEC assessment data is required","ocean":"OCEAN assessment data is required","viaIs":"VIA-IS assessment data is required","multipleIntelligences":"Multiple Intelligences assessment data is required","cognitiveStyleIndex":"Cognitive Style Index assessment data is required"},"url":"/test/submit"}
[2025-07-17T01:48:18.689Z] [INFO] ::1 - - [17/Jul/2025:01:48:18 +0000] "POST /test/submit HTTP/1.1" 400 381 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-17T01:48:27.720Z] [INFO] Test assessment submission received {"userId":"test-user-*************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::1"}
[2025-07-17T01:48:27.721Z] [INFO] Job created {"jobId":"7789597f-fa6c-42af-9ef7-cc94b1cf7bd0","userId":"test-user-*************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T01:48:27.723Z] [INFO] Assessment job published to queue {"jobId":"********-041c-4180-89ea-f973bde1d5ce","userId":"test-user-*************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T01:48:27.725Z] [INFO] ::1 - - [17/Jul/2025:01:48:27 +0000] "POST /test/submit HTTP/1.1" 202 341 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-17T03:04:32.191Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:04:32.261Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:04:32.262Z] [INFO] Queue service initialized successfully
[2025-07-17T03:04:32.263Z] [INFO] All services initialized successfully
[2025-07-17T03:04:32.268Z] [INFO] Assessment Service running on port 3003
[2025-07-17T03:04:32.269Z] [INFO] Environment: development
[2025-07-17T03:19:49.652Z] [WARN] RabbitMQ connection closed
[2025-07-17T03:19:54.660Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:19:54.661Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:19:54.664Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:19:54.664Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:19:59.676Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:19:59.676Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:19:59.679Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:19:59.680Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:04.690Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:04.690Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:04.692Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:20:04.692Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:09.708Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:09.709Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:09.711Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:20:09.711Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:14.714Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:14.715Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:14.717Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:20:14.717Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:19.730Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:19.730Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:19.732Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:20:19.733Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:24.748Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:24.749Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:24.769Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:21:23.338Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:21:23.363Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:21:23.363Z] [INFO] Queue service initialized successfully
[2025-07-17T03:21:23.364Z] [INFO] All services initialized successfully
[2025-07-17T03:27:55.340Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:27:55.370Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:27:55.371Z] [INFO] Queue service initialized successfully
[2025-07-17T03:27:55.371Z] [INFO] All services initialized successfully
[2025-07-17T03:27:55.374Z] [INFO] Assessment Service running on port 3003
[2025-07-17T03:27:55.374Z] [INFO] Environment: development
[2025-07-17T03:30:25.032Z] [INFO] ::1 - - [17/Jul/2025:03:30:25 +0000] "POST /api/assessment/analyze HTTP/1.1" 404 103 "-" "axios/1.10.0"
[2025-07-17T03:31:01.473Z] [WARN] Validation error {"source":"body","errors":{"riasec":"RIASEC assessment data is required","ocean":"OCEAN assessment data is required","viaIs":"VIA-IS assessment data is required","multipleIntelligences":"Multiple Intelligences assessment data is required","cognitiveStyleIndex":"Cognitive Style Index assessment data is required"},"url":"/test/submit"}
[2025-07-17T03:31:01.474Z] [INFO] ::1 - - [17/Jul/2025:03:31:01 +0000] "POST /test/submit HTTP/1.1" 400 381 "-" "axios/1.10.0"
[2025-07-17T03:31:57.024Z] [INFO] Test assessment submission received {"userId":"test-user-*************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::1"}
[2025-07-17T03:31:57.024Z] [INFO] Job created {"jobId":"310c97a6-2f28-4249-a5cf-7088bc11284d","userId":"test-user-*************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T03:31:57.026Z] [INFO] Assessment job published to queue {"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","userId":"test-user-*************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T03:31:57.028Z] [INFO] ::1 - - [17/Jul/2025:03:31:57 +0000] "POST /test/submit HTTP/1.1" 202 341 "-" "axios/1.10.0"
[2025-07-17T03:33:25.988Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:33:26.015Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:33:26.015Z] [INFO] Queue service initialized successfully
[2025-07-17T03:33:26.015Z] [INFO] All services initialized successfully
[2025-07-17T03:33:26.018Z] [INFO] Assessment Service running on port 3003
[2025-07-17T03:33:26.018Z] [INFO] Environment: development
[2025-07-17T03:33:48.755Z] [INFO] Test assessment submission received {"userId":"a976e5bc-2074-42de-854b-84f52293233f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::1"}
[2025-07-17T03:33:48.755Z] [INFO] Job created {"jobId":"4efbb3b2-10a0-445e-89b4-dfa108741232","userId":"a976e5bc-2074-42de-854b-84f52293233f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T03:33:48.757Z] [INFO] Assessment job published to queue {"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","userId":"a976e5bc-2074-42de-854b-84f52293233f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T03:33:48.763Z] [INFO] ::1 - - [17/Jul/2025:03:33:48 +0000] "POST /test/submit HTTP/1.1" 202 354 "-" "axios/1.10.0"
[2025-07-17T03:42:58.606Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:42:58.647Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:42:58.648Z] [INFO] Queue service initialized successfully
[2025-07-17T03:42:58.648Z] [INFO] All services initialized successfully
[2025-07-17T03:42:58.652Z] [INFO] Assessment Service running on port 3003
[2025-07-17T03:42:58.653Z] [INFO] Environment: development
[2025-07-17T03:47:33.760Z] [WARN] Validation error {"source":"body","errors":{"multipleIntelligences":"Multiple Intelligences assessment data is required","cognitiveStyleIndex":"Cognitive Style Index assessment data is required"},"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","url":"/assessments/submit"}
[2025-07-17T03:47:33.764Z] [INFO] ::1 - - [17/Jul/2025:03:47:33 +0000] "POST /assessments/submit HTTP/1.1" 400 246 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T03:49:39.676Z] [WARN] Validation error {"source":"body","errors":{"multipleIntelligences":"Multiple Intelligences assessment data is required","cognitiveStyleIndex":"Cognitive Style Index assessment data is required"},"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","url":"/assessments/submit"}
[2025-07-17T03:49:39.677Z] [INFO] ::1 - - [17/Jul/2025:03:49:39 +0000] "POST /assessments/submit HTTP/1.1" 400 246 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T04:00:12.609Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:00:12.657Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:00:12.658Z] [INFO] Queue service initialized successfully
[2025-07-17T04:00:12.658Z] [INFO] All services initialized successfully
[2025-07-17T04:00:40.249Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:00:40.287Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:00:40.288Z] [INFO] Queue service initialized successfully
[2025-07-17T04:00:40.288Z] [INFO] All services initialized successfully
[2025-07-17T04:00:40.291Z] [INFO] Assessment Service running on port 3003
[2025-07-17T04:00:40.291Z] [INFO] Environment: development
[2025-07-17T04:02:39.568Z] [INFO] Test assessment submission received {"userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T04:02:39.568Z] [INFO] Job created {"jobId":"dfb81f3b-a509-47fb-bf14-c7a99bff8409","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T04:02:39.571Z] [INFO] Assessment job published to queue {"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T04:02:39.577Z] [INFO] ::1 - - [17/Jul/2025:04:02:39 +0000] "POST /test/submit HTTP/1.1" 202 354 "-" "axios/1.10.0"
[2025-07-17T04:12:55.559Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:12:55.586Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:12:55.586Z] [INFO] Queue service initialized successfully
[2025-07-17T04:12:55.586Z] [INFO] All services initialized successfully
[2025-07-17T04:12:55.589Z] [INFO] Assessment Service running on port 3003
[2025-07-17T04:12:55.589Z] [INFO] Environment: development
[2025-07-17T04:14:23.018Z] [INFO] Test assessment submission received {"userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T04:14:23.018Z] [INFO] Job created {"jobId":"bbf87e5d-da94-47ea-8e43-322c39e1ad26","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T04:14:23.020Z] [INFO] Assessment job published to queue {"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T04:14:23.026Z] [INFO] ::1 - - [17/Jul/2025:04:14:23 +0000] "POST /test/submit HTTP/1.1" 202 354 "-" "axios/1.10.0"
[2025-07-17T04:27:53.076Z] [INFO] Test assessment submission received {"userId":"e9034651-8aa9-471b-97c7-71babc1ac589","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T04:27:53.077Z] [INFO] Job created {"jobId":"33aca03b-df0f-4549-bfae-87fda808c3c4","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T04:27:53.078Z] [INFO] Assessment job published to queue {"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T04:27:53.080Z] [INFO] ::1 - - [17/Jul/2025:04:27:53 +0000] "POST /test/submit HTTP/1.1" 202 354 "-" "axios/1.10.0"
[2025-07-17T04:40:05.573Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:40:05.616Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:40:05.616Z] [INFO] Queue service initialized successfully
[2025-07-17T04:40:05.617Z] [INFO] All services initialized successfully
[2025-07-17T04:40:22.600Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:40:22.637Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:40:22.638Z] [INFO] Queue service initialized successfully
[2025-07-17T04:40:22.639Z] [INFO] All services initialized successfully
[2025-07-17T04:40:22.643Z] [INFO] Assessment Service running on port 3003
[2025-07-17T04:40:22.643Z] [INFO] Environment: development
[2025-07-17T05:05:08.478Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T05:05:08.517Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T05:05:08.517Z] [INFO] Queue service initialized successfully
[2025-07-17T05:05:08.518Z] [INFO] All services initialized successfully
[2025-07-17T05:05:08.522Z] [INFO] Assessment Service running on port 3003
[2025-07-17T05:05:08.522Z] [INFO] Environment: development
[2025-07-17T05:13:16.498Z] [INFO] Assessment submission received {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T05:13:16.523Z] [INFO] Token deduction successful {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","deductedAmount":1,"remainingBalance":2}
[2025-07-17T05:13:16.524Z] [INFO] Job created {"jobId":"5ae4ad92-a815-4963-ad16-268637412a08","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T05:13:16.526Z] [INFO] Assessment job published to queue {"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T05:13:16.530Z] [INFO] ::1 - - [17/Jul/2025:05:13:16 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:16.586Z] [INFO] ::1 - - [17/Jul/2025:05:13:16 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:16.599Z] [INFO] ::1 - - [17/Jul/2025:05:13:16 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:21.582Z] [INFO] ::1 - - [17/Jul/2025:05:13:21 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:26.584Z] [INFO] ::1 - - [17/Jul/2025:05:13:26 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:31.580Z] [INFO] ::1 - - [17/Jul/2025:05:13:31 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:36.584Z] [INFO] ::1 - - [17/Jul/2025:05:13:36 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:41.579Z] [INFO] ::1 - - [17/Jul/2025:05:13:41 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:46.585Z] [INFO] ::1 - - [17/Jul/2025:05:13:46 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:51.592Z] [INFO] ::1 - - [17/Jul/2025:05:13:51 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:56.581Z] [INFO] ::1 - - [17/Jul/2025:05:13:56 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:14:01.581Z] [INFO] ::1 - - [17/Jul/2025:05:14:01 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:13.494Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T05:22:13.549Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T05:22:13.550Z] [INFO] Queue service initialized successfully
[2025-07-17T05:22:13.550Z] [INFO] All services initialized successfully
[2025-07-17T05:22:13.555Z] [INFO] Assessment Service running on port 3003
[2025-07-17T05:22:13.555Z] [INFO] Environment: development
[2025-07-17T05:22:48.361Z] [INFO] Assessment submission received {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T05:22:48.391Z] [INFO] Token deduction successful {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","deductedAmount":1,"remainingBalance":1}
[2025-07-17T05:22:48.392Z] [INFO] Job created {"jobId":"cf24392d-c8e0-466a-98c2-7c0459953605","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T05:22:48.394Z] [INFO] Assessment job published to queue {"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T05:22:48.398Z] [INFO] ::1 - - [17/Jul/2025:05:22:48 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:48.474Z] [INFO] ::1 - - [17/Jul/2025:05:22:48 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:48.491Z] [INFO] ::1 - - [17/Jul/2025:05:22:48 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:53.456Z] [INFO] ::1 - - [17/Jul/2025:05:22:53 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:58.462Z] [INFO] ::1 - - [17/Jul/2025:05:22:58 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:03.457Z] [INFO] ::1 - - [17/Jul/2025:05:23:03 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:08.467Z] [INFO] ::1 - - [17/Jul/2025:05:23:08 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:13.454Z] [INFO] ::1 - - [17/Jul/2025:05:23:13 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:18.462Z] [INFO] ::1 - - [17/Jul/2025:05:23:18 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:23.453Z] [INFO] ::1 - - [17/Jul/2025:05:23:23 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:28.460Z] [INFO] ::1 - - [17/Jul/2025:05:23:28 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:33.455Z] [INFO] ::1 - - [17/Jul/2025:05:23:33 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:38.459Z] [INFO] ::1 - - [17/Jul/2025:05:23:38 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:44.094Z] [INFO] ::1 - - [17/Jul/2025:05:23:44 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:49.088Z] [INFO] ::1 - - [17/Jul/2025:05:23:49 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:54.101Z] [INFO] ::1 - - [17/Jul/2025:05:23:54 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:59.094Z] [INFO] ::1 - - [17/Jul/2025:05:23:59 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:04.101Z] [INFO] ::1 - - [17/Jul/2025:05:24:04 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:09.087Z] [INFO] ::1 - - [17/Jul/2025:05:24:09 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:14.103Z] [INFO] ::1 - - [17/Jul/2025:05:24:14 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:19.092Z] [INFO] ::1 - - [17/Jul/2025:05:24:19 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:24.100Z] [INFO] ::1 - - [17/Jul/2025:05:24:24 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:29.087Z] [INFO] ::1 - - [17/Jul/2025:05:24:29 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:34.100Z] [INFO] ::1 - - [17/Jul/2025:05:24:34 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:39.087Z] [INFO] ::1 - - [17/Jul/2025:05:24:39 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:44.092Z] [INFO] ::1 - - [17/Jul/2025:05:24:44 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:49.098Z] [INFO] ::1 - - [17/Jul/2025:05:24:49 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:54.094Z] [INFO] ::1 - - [17/Jul/2025:05:24:54 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:59.096Z] [INFO] ::1 - - [17/Jul/2025:05:24:59 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:04.090Z] [INFO] ::1 - - [17/Jul/2025:05:25:04 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:06.124Z] [INFO] ::1 - - [17/Jul/2025:05:25:06 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:12.094Z] [INFO] ::1 - - [17/Jul/2025:05:25:12 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:17.087Z] [INFO] ::1 - - [17/Jul/2025:05:25:17 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:22.101Z] [INFO] ::1 - - [17/Jul/2025:05:25:22 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:24.107Z] [INFO] ::1 - - [17/Jul/2025:05:25:24 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:24.125Z] [INFO] ::1 - - [17/Jul/2025:05:25:24 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:27.096Z] [INFO] ::1 - - [17/Jul/2025:05:25:27 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:30.100Z] [INFO] ::1 - - [17/Jul/2025:05:25:30 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:32.097Z] [INFO] ::1 - - [17/Jul/2025:05:25:32 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:35.101Z] [INFO] ::1 - - [17/Jul/2025:05:25:35 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:40.092Z] [INFO] ::1 - - [17/Jul/2025:05:25:40 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:45.109Z] [INFO] ::1 - - [17/Jul/2025:05:25:45 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:47.085Z] [INFO] ::1 - - [17/Jul/2025:05:25:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:50.089Z] [INFO] ::1 - - [17/Jul/2025:05:25:50 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:55.093Z] [INFO] ::1 - - [17/Jul/2025:05:25:55 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:26:47.147Z] [INFO] ::1 - - [17/Jul/2025:05:26:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:26:47.162Z] [INFO] ::1 - - [17/Jul/2025:05:26:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:27:47.232Z] [INFO] ::1 - - [17/Jul/2025:05:27:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:27:47.244Z] [INFO] ::1 - - [17/Jul/2025:05:27:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:28:00.333Z] [INFO] ::1 - - [17/Jul/2025:05:28:00 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:28:01.532Z] [INFO] ::1 - - [17/Jul/2025:05:28:01 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:28:01.551Z] [INFO] ::1 - - [17/Jul/2025:05:28:01 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:28:06.523Z] [INFO] ::1 - - [17/Jul/2025:05:28:06 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:22.286Z] [INFO] Assessment submission received {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T05:31:22.301Z] [INFO] Token deduction successful {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","deductedAmount":1,"remainingBalance":0}
[2025-07-17T05:31:22.301Z] [INFO] Job created {"jobId":"24ae32ac-30e4-433f-a9d2-3429d8365bbf","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T05:31:22.302Z] [INFO] Assessment job published to queue {"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T05:31:22.305Z] [INFO] ::1 - - [17/Jul/2025:05:31:22 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:22.382Z] [INFO] ::1 - - [17/Jul/2025:05:31:22 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:22.397Z] [INFO] ::1 - - [17/Jul/2025:05:31:22 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:27.554Z] [INFO] ::1 - - [17/Jul/2025:05:31:27 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:32.374Z] [INFO] ::1 - - [17/Jul/2025:05:31:32 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:37.364Z] [INFO] ::1 - - [17/Jul/2025:05:31:37 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:42.363Z] [INFO] ::1 - - [17/Jul/2025:05:31:42 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:47.364Z] [INFO] ::1 - - [17/Jul/2025:05:31:47 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:15:26.292Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T11:15:26.346Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T11:15:26.347Z] [INFO] Queue service initialized successfully
[2025-07-17T11:15:26.348Z] [INFO] All services initialized successfully
[2025-07-17T11:15:26.353Z] [INFO] Assessment Service running on port 3003
[2025-07-17T11:15:26.354Z] [INFO] Environment: development
[2025-07-17T11:23:12.100Z] [INFO] Assessment submission received {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T11:23:12.122Z] [INFO] Token deduction successful {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","deductedAmount":1,"remainingBalance":2}
[2025-07-17T11:23:12.124Z] [INFO] Job created {"jobId":"fd21fdca-3b08-4b17-a0c1-011b783d263b","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T11:23:12.126Z] [INFO] Assessment job published to queue {"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T11:23:12.136Z] [INFO] ::1 - - [17/Jul/2025:11:23:12 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:12.228Z] [INFO] ::1 - - [17/Jul/2025:11:23:12 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:12.248Z] [INFO] ::1 - - [17/Jul/2025:11:23:12 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:17.207Z] [INFO] ::1 - - [17/Jul/2025:11:23:17 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:22.208Z] [INFO] ::1 - - [17/Jul/2025:11:23:22 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:27.206Z] [INFO] ::1 - - [17/Jul/2025:11:23:27 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:32.209Z] [INFO] ::1 - - [17/Jul/2025:11:23:32 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:37.207Z] [INFO] ::1 - - [17/Jul/2025:11:23:37 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:26:39.221Z] [INFO] Assessment submission received {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T11:26:39.242Z] [INFO] Token deduction successful {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","deductedAmount":1,"remainingBalance":1}
[2025-07-17T11:26:39.242Z] [INFO] Job created {"jobId":"1aaaeb17-041b-4b62-9105-4d94c195bdfe","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T11:26:39.243Z] [INFO] Assessment job published to queue {"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T11:26:39.247Z] [INFO] ::1 - - [17/Jul/2025:11:26:39 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:26:39.343Z] [INFO] ::1 - - [17/Jul/2025:11:26:39 +0000] "GET /assessments/status/1aaaeb17-041b-4b62-9105-4d94c195bdfe HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:26:39.360Z] [INFO] ::1 - - [17/Jul/2025:11:26:39 +0000] "GET /assessments/status/1aaaeb17-041b-4b62-9105-4d94c195bdfe HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:29.066Z] [INFO] Assessment submission received {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T12:31:29.089Z] [INFO] Token deduction successful {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","deductedAmount":1,"remainingBalance":0}
[2025-07-17T12:31:29.089Z] [INFO] Job created {"jobId":"8cf30219-ff08-486d-a412-2547e89ce80e","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T12:31:29.090Z] [INFO] Assessment job published to queue {"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T12:31:29.095Z] [INFO] ::1 - - [17/Jul/2025:12:31:29 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:29.177Z] [INFO] ::1 - - [17/Jul/2025:12:31:29 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:29.191Z] [INFO] ::1 - - [17/Jul/2025:12:31:29 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:34.157Z] [INFO] ::1 - - [17/Jul/2025:12:31:34 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:39.172Z] [INFO] ::1 - - [17/Jul/2025:12:31:39 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:44.158Z] [INFO] ::1 - - [17/Jul/2025:12:31:44 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:49.166Z] [INFO] ::1 - - [17/Jul/2025:12:31:49 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:54.157Z] [INFO] ::1 - - [17/Jul/2025:12:31:54 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:59.168Z] [INFO] ::1 - - [17/Jul/2025:12:31:59 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:04.456Z] [INFO] ::1 - - [17/Jul/2025:12:32:04 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:09.459Z] [INFO] ::1 - - [17/Jul/2025:12:32:09 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:14.446Z] [INFO] ::1 - - [17/Jul/2025:12:32:14 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:19.460Z] [INFO] ::1 - - [17/Jul/2025:12:32:19 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:24.448Z] [INFO] ::1 - - [17/Jul/2025:12:32:24 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:29.457Z] [INFO] ::1 - - [17/Jul/2025:12:32:29 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:32.457Z] [INFO] Assessment submission received {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T12:34:32.470Z] [INFO] Token deduction successful {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","deductedAmount":1,"remainingBalance":2}
[2025-07-17T12:34:32.471Z] [INFO] Job created {"jobId":"be8c6ad5-944b-4868-a6e1-61f690f50227","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T12:34:32.472Z] [INFO] Assessment job published to queue {"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T12:34:32.474Z] [INFO] ::1 - - [17/Jul/2025:12:34:32 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:32.561Z] [INFO] ::1 - - [17/Jul/2025:12:34:32 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:32.577Z] [INFO] ::1 - - [17/Jul/2025:12:34:32 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:37.546Z] [INFO] ::1 - - [17/Jul/2025:12:34:37 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:42.548Z] [INFO] ::1 - - [17/Jul/2025:12:34:42 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:47.540Z] [INFO] ::1 - - [17/Jul/2025:12:34:47 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:52.551Z] [INFO] ::1 - - [17/Jul/2025:12:34:52 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:31.734Z] [INFO] Assessment submission received {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:27:31.753Z] [INFO] Token deduction successful {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","deductedAmount":1,"remainingBalance":1}
[2025-07-17T13:27:31.754Z] [INFO] Job created {"jobId":"22c591c3-5e73-4891-bb5f-4a45f601ef29","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:27:31.754Z] [INFO] Assessment job published to queue {"jobId":"********-73f4-4bfe-8241-8b0e54829cda","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:27:31.760Z] [INFO] ::1 - - [17/Jul/2025:13:27:31 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:31.839Z] [INFO] ::1 - - [17/Jul/2025:13:27:31 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:31.855Z] [INFO] ::1 - - [17/Jul/2025:13:27:31 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:36.830Z] [INFO] ::1 - - [17/Jul/2025:13:27:36 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:41.827Z] [INFO] ::1 - - [17/Jul/2025:13:27:41 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:46.827Z] [INFO] ::1 - - [17/Jul/2025:13:27:46 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:51.834Z] [INFO] ::1 - - [17/Jul/2025:13:27:51 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:56.825Z] [INFO] ::1 - - [17/Jul/2025:13:27:56 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:01.932Z] [INFO] ::1 - - [17/Jul/2025:13:28:01 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:06.828Z] [INFO] ::1 - - [17/Jul/2025:13:28:06 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:11.847Z] [INFO] ::1 - - [17/Jul/2025:13:28:11 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:33.395Z] [INFO] Assessment submission received {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:28:33.409Z] [INFO] Token deduction successful {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","deductedAmount":1,"remainingBalance":0}
[2025-07-17T13:28:33.409Z] [INFO] Job created {"jobId":"82ebf263-9428-447b-88be-3147dbc016b0","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:28:33.410Z] [INFO] Assessment job published to queue {"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:28:33.413Z] [INFO] ::1 - - [17/Jul/2025:13:28:33 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:33.525Z] [INFO] ::1 - - [17/Jul/2025:13:28:33 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:33.538Z] [INFO] ::1 - - [17/Jul/2025:13:28:33 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:38.509Z] [INFO] ::1 - - [17/Jul/2025:13:28:38 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:43.505Z] [INFO] ::1 - - [17/Jul/2025:13:28:43 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:48.502Z] [INFO] ::1 - - [17/Jul/2025:13:28:48 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:53.504Z] [INFO] ::1 - - [17/Jul/2025:13:28:53 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:58.504Z] [INFO] ::1 - - [17/Jul/2025:13:28:58 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:03.505Z] [INFO] ::1 - - [17/Jul/2025:13:29:03 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:08.510Z] [INFO] ::1 - - [17/Jul/2025:13:29:08 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:13.504Z] [INFO] ::1 - - [17/Jul/2025:13:29:13 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:18.504Z] [INFO] ::1 - - [17/Jul/2025:13:29:18 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:24.445Z] [INFO] ::1 - - [17/Jul/2025:13:29:24 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:29.454Z] [INFO] ::1 - - [17/Jul/2025:13:29:29 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:34.440Z] [INFO] ::1 - - [17/Jul/2025:13:29:34 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:38.510Z] [INFO] ::1 - - [17/Jul/2025:13:29:38 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:39.644Z] [INFO] ::1 - - [17/Jul/2025:13:29:39 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:44.650Z] [INFO] ::1 - - [17/Jul/2025:13:29:44 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:49.648Z] [INFO] ::1 - - [17/Jul/2025:13:29:49 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:52.399Z] [INFO] ::1 - - [17/Jul/2025:13:29:52 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:46.312Z] [INFO] Assessment submission received {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:33:46.328Z] [INFO] Token deduction successful {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","deductedAmount":1,"remainingBalance":2}
[2025-07-17T13:33:46.329Z] [INFO] Job created {"jobId":"830de7aa-050e-4ff5-9595-04939f6fbc83","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:33:46.330Z] [INFO] Assessment job published to queue {"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:33:46.332Z] [INFO] ::1 - - [17/Jul/2025:13:33:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:46.410Z] [INFO] ::1 - - [17/Jul/2025:13:33:46 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:46.425Z] [INFO] ::1 - - [17/Jul/2025:13:33:46 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:51.398Z] [INFO] ::1 - - [17/Jul/2025:13:33:51 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:56.401Z] [INFO] ::1 - - [17/Jul/2025:13:33:56 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:01.396Z] [INFO] ::1 - - [17/Jul/2025:13:34:01 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:05.235Z] [INFO] ::1 - - [17/Jul/2025:13:34:05 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:06.395Z] [INFO] ::1 - - [17/Jul/2025:13:34:06 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:11.400Z] [INFO] ::1 - - [17/Jul/2025:13:34:11 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:16.398Z] [INFO] ::1 - - [17/Jul/2025:13:34:16 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:36:15.527Z] [INFO] ::1 - - [17/Jul/2025:13:36:15 +0000] "GET /health HTTP/1.1" 200 593 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-17T13:38:31.260Z] [INFO] Assessment submission received {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:38:31.272Z] [INFO] Token deduction successful {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","deductedAmount":1,"remainingBalance":1}
[2025-07-17T13:38:31.273Z] [INFO] Job created {"jobId":"e9bfeafd-1975-4f4c-ad74-aa3941858b9b","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:38:31.274Z] [INFO] Assessment job published to queue {"jobId":"00cd0b6d-afa3-41a2-899d-8cd3ee662438","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:38:31.283Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 404 (NOT-FOUND) with message \"NOT_FOUND - no queue 'assessment_analysis' in vhost '/'\""}
[2025-07-17T13:38:31.286Z] [WARN] RabbitMQ connection closed
[2025-07-17T13:38:31.286Z] [ERROR] Failed to get queue statistics {"error":"Operation failed: QueueDeclare; 404 (NOT-FOUND) with message \"NOT_FOUND - no queue 'assessment_analysis' in vhost '/'\""}
[2025-07-17T13:38:31.287Z] [INFO] ::1 - - [17/Jul/2025:13:38:31 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:31.366Z] [INFO] ::1 - - [17/Jul/2025:13:38:31 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:31.379Z] [INFO] ::1 - - [17/Jul/2025:13:38:31 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:36.299Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Channel closed by server: 404 (NOT-FOUND) with message \"NOT_FOUND - no queue 'assessment_analysis' in vhost '/'\")"}
[2025-07-17T13:38:36.299Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T13:38:36.304Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Channel closed by server: 404 (NOT-FOUND) with message \"NOT_FOUND - no queue 'assessment_analysis' in vhost '/'\")"}
[2025-07-17T13:38:36.305Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T13:38:36.328Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T13:38:36.330Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T13:38:36.354Z] [INFO] ::1 - - [17/Jul/2025:13:38:36 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:41.355Z] [INFO] ::1 - - [17/Jul/2025:13:38:41 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:44.549Z] [INFO] ::1 - - [17/Jul/2025:13:38:44 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:45.039Z] [INFO] ::1 - - [17/Jul/2025:13:38:45 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:49.553Z] [INFO] ::1 - - [17/Jul/2025:13:38:49 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:54.548Z] [INFO] ::1 - - [17/Jul/2025:13:38:54 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:59.552Z] [INFO] ::1 - - [17/Jul/2025:13:38:59 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:39:04.551Z] [INFO] ::1 - - [17/Jul/2025:13:39:04 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:39:09.552Z] [INFO] ::1 - - [17/Jul/2025:13:39:09 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:39:14.548Z] [INFO] ::1 - - [17/Jul/2025:13:39:14 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:39:19.553Z] [INFO] ::1 - - [17/Jul/2025:13:39:19 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:46:25.275Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T13:46:25.317Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T13:46:25.318Z] [INFO] Queue service initialized successfully
[2025-07-17T13:46:25.318Z] [INFO] All services initialized successfully
[2025-07-17T13:46:25.322Z] [INFO] Assessment Service running on port 3003
[2025-07-17T13:46:25.322Z] [INFO] Environment: development
[2025-07-17T13:46:59.667Z] [INFO] Assessment submission received {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:46:59.779Z] [INFO] Token deduction successful {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","deductedAmount":1,"remainingBalance":0}
[2025-07-17T13:46:59.780Z] [INFO] Job created {"jobId":"72cf577d-54c2-4786-89bd-701b4ff76629","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:46:59.782Z] [INFO] Assessment job published to queue {"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:46:59.788Z] [INFO] ::1 - - [17/Jul/2025:13:46:59 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:46:59.927Z] [INFO] ::1 - - [17/Jul/2025:13:46:59 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:46:59.948Z] [INFO] ::1 - - [17/Jul/2025:13:46:59 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:47:09.918Z] [INFO] ::1 - - [17/Jul/2025:13:47:09 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:47:19.916Z] [INFO] ::1 - - [17/Jul/2025:13:47:19 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:47:20.889Z] [INFO] ::1 - - [17/Jul/2025:13:47:20 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:47:29.917Z] [INFO] ::1 - - [17/Jul/2025:13:47:29 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
